/* User Dashboard Layout Container */
.user-dashboard-layout-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Blur Gradients */
.user-dashboard-layout-container .blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.user-dashboard-layout-container .blur-gradient-1 {
  width: 316px;
  height: 316px;
  left: -150px;
  top: -150px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(200px);
}

.user-dashboard-layout-container .blur-gradient-2 {
  width: 239px;
  height: 239px;
  left: -50px;
  top: -50px;
  background: #D19049;
  opacity: 0.15;
  filter: blur(150px);
}

.user-dashboard-layout-container .blur-gradient-3 {
  width: 400px;
  height: 400px;
  right: -200px;
  top: 200px;
  background: #BF4129;
  opacity: 0.1;
  filter: blur(250px);
}

.user-dashboard-layout-container .blur-gradient-4 {
  width: 300px;
  height: 300px;
  left: 50%;
  top: 600px;
  transform: translateX(-50%);
  background: #F0C369;
  opacity: 0.08;
  filter: blur(200px);
}

.user-dashboard-layout-container .blur-gradient-5 {
  width: 350px;
  height: 350px;
  right: -100px;
  bottom: 200px;
  background: #CC6754;
  opacity: 0.12;
  filter: blur(220px);
}

/* Main Content */
.user-dashboard-main {
  padding-top: 120px;
  padding-bottom: 80px;
  min-height: calc(100vh - 120px);
  position: relative;
  z-index: 2;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-dashboard-layout-container .blur-gradient-1 {
    width: 200px;
    height: 200px;
    left: -80px;
    top: 60px;
  }

  .user-dashboard-layout-container .blur-gradient-2 {
    width: 150px;
    height: 150px;
    right: -60px;
    top: 250px;
  }

  .user-dashboard-layout-container .blur-gradient-3 {
    width: 180px;
    height: 180px;
    left: calc(50% - 180px/2 - 20px);
    top: 180px;
  }

  .user-dashboard-layout-container .blur-gradient-4 {
    width: 180px;
    height: 180px;
    right: -30px;
    top: 220px;
  }

  .user-dashboard-layout-container .blur-gradient-5 {
    width: 160px;
    height: 160px;
    left: calc(50% - 160px/2 + 80px);
    top: 400px;
  }

  .user-dashboard-main {
    padding-top: 80px;
    padding-bottom: 60px;
  }
}
