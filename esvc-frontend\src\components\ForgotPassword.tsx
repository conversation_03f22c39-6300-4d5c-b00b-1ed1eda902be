import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/ForgotPassword.css';

interface ForgotPasswordProps {
  onBack: () => void;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onBack }) => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'forgot' | 'verify' | 'reset'>('forgot');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isCodeInvalid, setIsCodeInvalid] = useState(false);
  const [resendTimer, setResendTimer] = useState(119); // 1:59 in seconds

  // Timer countdown effect
  useEffect(() => {
    if (step === 'verify' && resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [step, resendTimer]);

  const handleSendCode = () => {
    // Simulate sending verification code
    setStep('verify');
    setResendTimer(119); // Reset timer
  };

  const handleChangeEmailAddress = () => {
    setStep('forgot');
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
  };

  const handleResendCode = () => {
    // Simulate resending code
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
    setResendTimer(119);
  };

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      setIsCodeInvalid(false);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate code verification
      if (code === '123456') { // Mock valid code
        setStep('reset');
      } else {
        setIsCodeInvalid(true);
      }
    }
  };

  const handleResetPassword = () => {
    // Simulate password reset
    console.log('Password reset successfully');
    onBack(); // Return to login
  };

  const isPasswordValid = (password: string) => {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    return checks;
  };

  const passwordChecks = isPasswordValid(newPassword);
  const allPasswordChecksValid = Object.values(passwordChecks).every(Boolean);
  const passwordsMatch = newPassword === confirmPassword && confirmPassword.length > 0;

  if (step === 'forgot') {
    return (
      <div className="forgot-password-container">
        <div className="forgot-password-content">
          <button className="back-button" onClick={onBack}>
            <span>←</span> Go Back
          </button>
          
          <div className="forgot-password-form">
            <div>
              <h1>Forgot Your Password?</h1>
              <p>We'll send you a verification code to reset your password</p>
            </div>

            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <div className="input-wrapper">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="form-input"
                />
              </div>
            </div>

            <button
              className="btn-primary"
              onClick={handleSendCode}
              disabled={!email}
            >
              Send Verification Code
            </button>

            <button className="change-email-btn" onClick={() => navigate('/login')}>
              <span>←</span> Back to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (step === 'verify') {
    return (
      <div className="forgot-password-container">
        <div className="forgot-password-content">
          <button className="back-button" onClick={onBack}>
            <span>←</span> Go Back
          </button>
          
          <div className="forgot-password-form verify-form">
            <div>
              <h1>Verify Your Email</h1>
              <p>Enter the 6-digit code sent to your email <strong>{email}</strong> to continue</p>
            </div>

            <div className="verification-code">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  id={`code-${index}`}
                  type="text"
                  value={digit}
                  onChange={(e) => handleCodeChange(index, e.target.value)}
                  className={`code-input ${isCodeInvalid ? 'invalid' : ''}`}
                  maxLength={1}
                />
              ))}
            </div>

            {isCodeInvalid && (
              <p className="error-message">Invalid or expired code. Please check and try again.</p>
            )}

            <button
              className="btn-primary"
              onClick={handleVerifyCode}
              disabled={verificationCode.join('').length !== 6}
            >
              Verify Code
            </button>

            <div className="resend-section">
              <p>Check your spam folder if you don't see it.</p>
              <button
                className="resend-link"
                onClick={handleResendCode}
                disabled={resendTimer > 0}
              >
                {resendTimer > 0
                  ? `Resend Code in ${Math.floor(resendTimer / 60)}:${(resendTimer % 60).toString().padStart(2, '0')}`
                  : 'Resend Code'
                }
              </button>
            </div>

            <button className="change-email-btn" onClick={handleChangeEmailAddress}>
              <span>←</span> Change Email Address
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="forgot-password-container">
      <div className="forgot-password-content">
        <button className="back-button" onClick={onBack}>
          <span>←</span> Go Back
        </button>
        
        <div className="forgot-password-form verify-form">
          <div>
            <h1>Set a New Password</h1>
            <p>Create a strong password for your account</p>
          </div>

          <div className="form-group">
            <label htmlFor="new-password">New Password</label>
            <div className="input-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="new-password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="••••••••"
                className="form-input"
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword(!showPassword)}
              >
                👁
              </button>
            </div>
          </div>

          <div className="password-requirements">
            <div className={`requirement ${passwordChecks.length ? 'valid' : ''}`}>
              Be at least 8 characters long
            </div>
            <div className={`requirement ${passwordChecks.uppercase ? 'valid' : ''}`}>
              Include at least one uppercase letter (A-Z)
            </div>
            <div className={`requirement ${passwordChecks.lowercase ? 'valid' : ''}`}>
              Include at least one lowercase letter (a-z)
            </div>
            <div className={`requirement ${passwordChecks.number ? 'valid' : ''}`}>
              Include at least one number (0-9)
            </div>
            <div className={`requirement ${passwordChecks.special ? 'valid' : ''}`}>
              Include at least one special character (!@#$%^&*)
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="confirm-password">Re-Enter New Password</label>
            <div className="input-wrapper">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirm-password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Re-enter your new password"
                className="form-input"
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                👁
              </button>
            </div>
          </div>

          <button
            className="btn-primary"
            onClick={handleResetPassword}
            disabled={!allPasswordChecksValid || !passwordsMatch}
          >
            Reset Password
          </button>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
