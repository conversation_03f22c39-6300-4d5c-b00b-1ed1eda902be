import React, { useState } from 'react';
import '../styles/components/RealTimeStaking.css';
import DashboardLayout from './DashboardLayout';
import SideNav from './SideNav';

// Import icons
import spanImage from '../assets/span.png';
import esvcToken from '../assets/esvc-token.png';
import trendUpIcon from '../assets/trend-up.png';

interface RealTimeStakingProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const RealTimeStaking: React.FC<RealTimeStakingProps> = () => {
  const [activeTab, setActiveTab] = useState('real-time-staking');
  const [timeFilter, setTimeFilter] = useState('today');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;



  const timeFilters = [
    { id: 'today', label: 'Today' },
    { id: 'week', label: 'This week' },
    { id: 'month', label: 'This month' },
    { id: 'all-time', label: 'All time' }
  ];

  // Mock staking data
  const stakingData = Array.from({ length: 50 }, (_, index) => {
    const stakingPeriods = ['6 Months', '12 Months'];
    const amounts = [900, 1100, 640, 400, 10500, 3800];
    const period = stakingPeriods[index % 2];
    const amount = amounts[index % amounts.length];
    
    return {
      id: index + 1,
      type: 'ESVC Staked',
      period: period,
      recipient: 'From 0x6d4...bF1',
      amount: `${amount} ESVC`,
      time: 'Today, 10:17 AM',
      icon: esvcToken,
      indicator: trendUpIcon
    };
  });

  const totalValue = '67,340';



  const totalPages = Math.ceil(stakingData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentStakingData = stakingData.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const centerPages = [];
    const maxVisiblePages = 5;

    // Page numbers for center
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      centerPages.push(
        <button
          key={i}
          className={`pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    // Add ellipsis if needed
    if (startPage > 1) {
      centerPages.unshift(
        <span key="start-ellipsis" className="pagination-ellipsis">...</span>
      );
    }
    if (endPage < totalPages) {
      centerPages.push(
        <span key="end-ellipsis" className="pagination-ellipsis">...</span>
      );
    }

    return (
      <>
        {/* Previous button */}
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>

        {/* Center page numbers */}
        <div className="pagination-center">
          {centerPages}
        </div>

        {/* Next button */}
        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next →
        </button>
      </>
    );
  };

  return (
    <DashboardLayout className="real-time-staking-container">
      <div className="real-time-staking-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <SideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="staking-header">
              <h2 className="section-title">Real-Time Staking</h2>
            </div>

            {/* Filters and Total */}
            <div className="staking-controls">
              <div className="time-filter">
                <select
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value)}
                  className="filter-select"
                >
                  {timeFilters.map((filter) => (
                    <option key={filter.id} value={filter.id}>
                      {filter.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="total-value-card">
                <div className="total-label">TOTAL STAKED (BASED ON FILTER DATE)</div>
                <div className="total-amount">{totalValue}</div>
              </div>
            </div>

            {/* Staking List */}
            <div className="staking-list">
              {currentStakingData.map((staking, index) => (
                <div key={staking.id} className="staking-item">
                  <div className="staking-number">{startIndex + index + 1}.</div>
                  <div className="staking-icon">
                    <img src={staking.icon} alt="Staking icon" />
                  </div>
                  <div className="staking-details">
                    <div className="staking-type">{staking.type} • {staking.period}</div>
                    <div className="staking-recipient">{staking.recipient}</div>
                  </div>
                  <div className="staking-amount-time">
                    <div className="staking-amount">{staking.amount}</div>
                    <div className="staking-time">{staking.time}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="pagination">
              {renderPagination()}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default RealTimeStaking;
