import React, { useState } from 'react';
import '../../styles/components/user-dashboard/MyStake.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';

interface MyStakeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const MyStake: React.FC<MyStakeProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [activeTab, setActiveTab] = useState('my-stake');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  return (
    <UserDashboardLayout className="my-stake-container">
      <div className="my-stake-content">
        {/* User Greeting Header - Reusing from Overview */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Dashboard Content */}
          <div className="dashboard-content">
            {/* Wallet Selector Section */}
            <div className="wallet-selector-section">
              <div className="wallet-selector-card">
                <select className="wallet-dropdown">
                  <option value="wallet1">🔒 Wallet 1 ($10,000 stakes)</option>
                  <option value="wallet2">🔒 Wallet 2 ($5,000 stakes)</option>
                  <option value="wallet3">🔒 Wallet 3 ($15,000 stakes)</option>
                </select>
              </div>
            </div>

            {/* Stake Details Section */}
            <div className="stake-details-section">
              <div className="stake-details-card">
                <div className="stake-summary-row">
                  <div className="stake-amount-section">
                    <div className="amount-label">Amount Staked</div>
                    <div className="amount-value">
                      {showBalances ? '788.50' : '***.**'}
                      <span className="amount-unit">ESVC</span>
                    </div>
                    <div className="amount-usd">at {showBalances ? '$10,000' : '$***,***'}</div>
                  </div>

                  <div className="roi-stats-section">
                    <div className="roi-stat">
                      <div className="roi-value">{showBalances ? '$700' : '$***'}</div>
                      <div className="roi-label">ROI earned so far</div>
                    </div>
                    <div className="roi-stat">
                      <div className="roi-value">{showBalances ? '$2,000' : '$***'}</div>
                      <div className="roi-label">Total Expected ROI</div>
                    </div>
                  </div>

                  <div className="withdraw-section">
                    <button className="withdraw-earned-btn">
                      Withdraw Earned ROI
                    </button>
                  </div>
                </div>

                <div className="stake-info-grid">
                  <div className="info-row">
                    <span className="info-label">STATUS</span>
                    <span className="info-value status-active">Active</span>
                  </div>
                  <div className="info-row">
                    <span className="info-label">LOCK PERIOD</span>
                    <span className="info-value">12 months</span>
                  </div>
                  <div className="info-row">
                    <span className="info-label">DATE STAKED</span>
                    <span className="info-value">Jan 3, 2025</span>
                  </div>
                  <div className="info-row">
                    <span className="info-label">UNSTAKE DATE</span>
                    <span className="info-value">Jan 3, 2026</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default MyStake;
