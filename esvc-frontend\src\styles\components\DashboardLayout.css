/* Dashboard Layout Container */
.dashboard-layout-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Blur Gradients */
.blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.blur-gradient-1 {
  width: 316px;
  height: 316px;
  left: -150px;
  top: -150px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(200px);
}

.blur-gradient-2 {
  width: 239px;
  height: 239px;
  left: -50px;
  top: -50px;
  background: #D19049;
  opacity: 0.15;
  filter: blur(150px);
}

.blur-gradient-3 {
  position: absolute;
  width: 239px;
  height: 239px;
  left: calc(50% - 239px/2 - 29.5px);
  top: 233px;
  background: linear-gradient(104.9deg, #D19049 9.22%, #BF4129 78.37%);
  opacity: 0.2;
  filter: blur(100px);
  z-index: 1;
}

.blur-gradient-4 {
  position: absolute;
  width: 239px;
  height: 239px;
  right: 0px;
  top: 304px;
  background: #D19049;
  opacity: 0.3;
  filter: blur(100px);
  z-index: 1;
}

.blur-gradient-5 {
  position: absolute;
  width: 239px;
  height: 239px;
  left: calc(50% - 239px/2 + 200px);
  top: 500px;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  opacity: 0.15;
  filter: blur(120px);
  z-index: 1;
}

/* Main Content */
.dashboard-main {
  padding-top: 120px;
  padding-bottom: 80px;
  min-height: calc(100vh - 120px);
  position: relative;
  z-index: 2;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .blur-gradient-1 {
    width: 200px;
    height: 200px;
    left: -80px;
    top: 60px;
  }

  .blur-gradient-2 {
    width: 150px;
    height: 150px;
    right: -60px;
    top: 250px;
  }

  .blur-gradient-3 {
    width: 180px;
    height: 180px;
    left: calc(50% - 180px/2 - 20px);
    top: 180px;
  }

  .blur-gradient-4 {
    width: 180px;
    height: 180px;
    right: -30px;
    top: 220px;
  }

  .blur-gradient-5 {
    width: 160px;
    height: 160px;
    left: calc(50% - 160px/2 + 80px);
    top: 400px;
  }

  .dashboard-main {
    padding-top: 100px;
  }
}
