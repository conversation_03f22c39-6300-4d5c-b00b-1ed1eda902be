/* Import Montserrat font */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

:root {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* ESVC Color Palette - Exact Figma Colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #262626;
  --bg-card: #2d2d2d;
  --text-primary: #F5F5F5;
  --text-secondary: #D4D4D4;
  --text-muted: #A3A3A3;
  --accent-orange: #BF4129;
  --accent-orange-hover: #A63622;
  --accent-gold: #F0C369;
  --border-color: #404040;
  --border-secondary: #525252;

  color: var(--text-primary);
  background-color: var(--bg-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, #1f1f1f 50%, var(--bg-primary) 100%);
  color: var(--text-primary);
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

p, span, div, a, button {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--accent-orange-hover), #e85a4f);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(232, 90, 79, 0.6);
  }
  100% {
    box-shadow: 0 8px 25px rgba(232, 90, 79, 0.4);
  }
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  padding: 12px 24px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-orange);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-orange-hover);
}
